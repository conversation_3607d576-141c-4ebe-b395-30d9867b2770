<template>
  <el-dialog
    :title="currentLaw ? '更新法规' : '新增法规'"
    :visible="true"
    class="add-law-dialog"
    width="510px"
    v-loading="loading"
    :close-on-click-modal="false"
    :modal-append-to-body="false"
    @close="closeDialog">
    <div>
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="100px"
        label-position="left">
        <el-form-item label="获取方式">
          <el-radio-group v-model="form.method">
            <el-radio label="manual">手动上传</el-radio>
            <el-radio label="local">本地法规库</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="应用场景" prop="scenario_ids">
          <el-select
            multiple
            :disabled="!!currentLaw"
            v-model="form.scenario_ids"
            placeholder=""
            style="width: 100%">
            <el-option
              v-for="item in typeOptions"
              :key="item.id"
              :value="item.id"
              :label="item.name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="法规库" v-if="form.method === 'local'">
          <el-select v-model="form.library">
            <el-option
              v-for="item in libraryOptions"
              :key="item.key"
              :value="item.value"
              :label="item.label"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <label class="custom-upload" v-if="form.method === 'manual'">
        <label @click="clickUploadBtn">
          <div class="drop-area" @drop.prevent="handleDrop" @dragover.prevent>
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              仅支持上传 doc、docx、pdf 格式的文档
            </div>
            <div class="el-upload__text">
              点击此区域上传文件或拖拽文件到此区域
            </div>
          </div>
        </label>
        <input
          type="file"
          ref="uploadInput"
          name="file"
          class="upload-input"
          multiple
          @change="uploadFileData"
          accept=".doc,.docx,.pdf" />
      </label>
      <div class="file-list">
        <div class="file-item" v-for="(file, index) in files" :key="index">
          <span>{{ file.name }}</span>
          <theme-icon
            class="delete-btn"
            name="delete"
            icon-class="el-icon-delete"
            @click.native.stop="deleteFile(index)"></theme-icon>
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel" size="small">取消</el-button>
      <el-button type="primary" @click="handleConfirm" size="small">
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'add-law-dialog',
  props: {
    typeOptions: {
      type: Array,
      default: () => [],
    },
    currentLaw: {
      type: [Object, null],
      default: () => {},
    },
  },
  data() {
    return {
      form: {
        method: 'manual',
        scenario_ids: [],
        library: '',
      },
      files: [],
      libraryOptions: [],
      rules: {
        scenario_ids: [
          { required: true, message: '请选择应用场景', trigger: 'change' },
        ],
      },
      loading: false,
    };
  },
  created() {
    if (this.currentLaw) {
      const scenario_ids = this.currentLaw.scenarios.map((item) => item.id);
      if (scenario_ids.length) {
        this.form = {
          scenario_ids,
          method: 'manual',
          library: '',
        };
      }
    }
  },
  computed: {
    isEdit() {
      return !!this.currentLaw;
    },
  },
  methods: {
    handleBeforeUpload(files) {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const fileType = file.name
          .substr(file.name.lastIndexOf('.'))
          .toLowerCase();

        if (!['.doc', '.docx', '.pdf'].includes(fileType)) {
          this.$message.error('文件格式错误，请重新上传');
          return false;
        }
      }

      return true;
    },
    handleDrop(e) {
      const fileList = [...e.dataTransfer.files];
      if (this.isEdit && [...this.files, ...fileList].length > 1) {
        this.$message.error('最多上传一个文件');
        return;
      }
      if (!this.handleBeforeUpload(fileList)) {
        return;
      }
      this.files = [...this.files, ...fileList];
    },
    clickUploadBtn() {
      this.$refs.uploadInput.value = null;
      this.$refs.uploadInput.files = null;
      this.$refs.uploadInput.click();
    },
    uploadFileData(event) {
      const fileList = [...event.target.files];
      if (this.isEdit && [...this.files, ...fileList].length > 1) {
        this.$message.error('最多上传一个文件');
        return;
      }
      if (!this.handleBeforeUpload(fileList)) {
        return;
      }
      this.files = [...this.files, ...fileList];
    },
    deleteFile(index) {
      this.files.splice(index, 1);
    },
    closeDialog() {
      this.$emit('close');
    },
    handleCancel() {
      this.closeDialog();
    },
    closeLoading() {
      this.loading = false;
    },
    async handleConfirm() {
      const valid = await this.$refs.form.validate().catch(() => {});
      if (!valid) {
        return;
      }

      if (this.form.method === 'manual') {
        if (this.files.length === 0) {
          this.$notify({
            title: '错误',
            message: '请上传文件',
            type: 'error',
          });
          return;
        }
      }

      this.loading = true;
      if (this.isEdit) {
        this.$emit(
          'confirm',
          this.form.method === 'manual' ? { files: this.files } : {},
        );
      } else {
        this.$emit(
          'confirm',
          this.form.method === 'manual'
            ? {
                form: { scenario_ids: this.form.scenario_ids },
                files: this.files,
              }
            : { form: this.form, files: this.files },
        );
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.upload-input {
  display: none;
}
.custom-upload {
  width: 100%;
  .drop-area {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    width: 100%;
    height: 150px;
    background: #f8fafc;
    cursor: pointer;
    .el-icon-upload {
      font-size: 50px;
      color: #c0c4cc;
    }
    .el-upload__text {
      color: #9a9a9a;
    }
  }
}
.file-list {
  margin-top: 10px;
  max-height: 160px;
  overflow: auto;
  .file-item {
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;

    &:hover {
      color: $--color-primary;
      background: #f3f7fc;
    }

    > span {
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }

    .delete-btn {
      cursor: pointer;
    }
  }
}
</style>
